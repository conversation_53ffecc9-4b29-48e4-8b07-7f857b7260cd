package com.mainline.mcp.newPv;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.Set;

import com.mainline.mcp.dto.newPv.BizExpenseDto;
import com.mainline.mcp.dto.newPv.LayoutColumn;
import com.mainline.mcp.dto.newPv.MetaKeyDto;
import com.mainline.mcp.dto.newPv.MetaKeyValueDto;
import com.mainline.mcp.dto.newPv.PvCollatInfoDto;
import com.mainline.mcp.dto.newPv.RateKeyOutDto;
import com.mainline.mcp.dto.newPv.RateKeyOutFrmtDto;
import com.mainline.mcp.dto.newPv.mp.MPStatistic;

import kr.dazzle.log.ILogger;
import kr.dazzle.util.EnBeanUtility;
import kr.dazzle.util.EnUtility;

public abstract class BasePvDataConvLayout {
	
	public List<String[]> makePvDataConvLayout(PvCollatInfoDto pvCollatInfoDto, RateKeyOutDto rateKeyOutDto, BizExpenseDto[] bizExpDtos, String meta, String homePath) {
		return makePvDataConvLayout(pvCollatInfoDto, rateKeyOutDto, null, bizExpDtos, meta, homePath);
	}
	
	public List<String[]> makePvDataConvLayout(PvCollatInfoDto pvCollatInfoDto, RateKeyOutDto rateKeyOutDto, LayoutColumn[] layoutColumns, BizExpenseDto[] bizExpDtos, String meta, String homePath) {
		if (layoutColumns != null) {
			return makeUsedOutFormPvDataConvLayout(pvCollatInfoDto, rateKeyOutDto, layoutColumns, bizExpDtos, meta, homePath);
		}
		return makeUnusedOutFormPvDataConvLayout(pvCollatInfoDto, rateKeyOutDto, bizExpDtos, meta, homePath);
	}

	protected List<String[]> makeUsedOutFormPvDataConvLayout(PvCollatInfoDto pvCollatInfoDto, RateKeyOutDto rateKeyOutDto, LayoutColumn[] layoutColumns, BizExpenseDto[] bizExpDtos, String meta, String homePath) {
		return makeUnusedOutFormPvDataConvLayout(pvCollatInfoDto, rateKeyOutDto, bizExpDtos, meta, homePath);
	}
	
	protected List<String[]> makeUnusedOutFormPvDataConvLayout(PvCollatInfoDto pvCollatInfoDto, RateKeyOutDto rateKeyOutDto, BizExpenseDto[] bizExpDtos, String meta, String homePath) {
		List<String[]> rootList = new ArrayList<>();
		
		System.out.println(pvCollatInfoDto.getIdByCodes() + ":" + rateKeyOutDto.layoutName);
		
		// 레이아웃 생성 시간 측정
		long time = System.currentTimeMillis();
		
		if (pvCollatInfoDto == null || rateKeyOutDto == null) {
			printLog(ILogger.MODE_CHAR_ERROR, "Can't found Dto.");
			return rootList;
		}
		
		// 변환 대상 경로
		Path basePath = getOutputFileBasePath(homePath);
		Path colBasePath = getColumnFileBasePath(homePath);
		
		// 사용 출력 유형 ex) 배열준비금, verticalRange 등
		boolean _isHorizontalSplitArray = false;
		boolean _isVerticalArray = false;
		Map<String, Boolean> useMetaTypeMap = new HashMap<>();
		if (EnUtility.isNotBlank(meta)) {
			for (String detailMetaType : meta.trim().split(" *, *")) {
				if (RateKeyOutDto.H_SPLIT_RANGE.equals(detailMetaType)) {
					_isHorizontalSplitArray = true;
				} else if (RateKeyOutDto.V_RANGE.equals(detailMetaType)) {
					_isVerticalArray = true;
				}
				
				useMetaTypeMap.put(detailMetaType, true);
			}
		}
		
		// 배열값을 가진 컬럼의 변환 후 인덱스
		// * 해당 컬럼은 기본적으로 포멧이 적용되지 않는다. verticalRange 인 경우 포멧 적용됨.
		List<Integer> arrayValueColumnIndex_afterConvert = new ArrayList<>();
		
		// '계약자적립금', '해약환급금' 컬럼의 변환 후 인덱스
		// * 기시 배열의 마지막에 기말 배열의 마지막 값을 추가
		Map<String, Integer> horizontalArrayIdxMap = new HashMap<>();
		
		// 배열값을 가진 컬럼의 변환 전 인덱스
		// * 기시 배열의 마지막에 기말 배열의 마지막 값을 추가 - '계약자적립금', '해약환급금'
		Map<String, Integer> arrayIdxMap = new HashMap<>();
		
		// 사업비 컬럼명 인덱스
		// * 변환 전 컬럼에 정해진 순서로 사업비 컬럼 추가한 index
		Map<String, Integer> bizExpKeyIdxMap = new HashMap<>();
		
		// 메타키 값 변환 정보
		// <PIA, <1 , 001>>
		Map<String, Map<String, String>> metaKeyDataConv = new HashMap<>();
		
		int verticalSqlIdx = -1;
		int addSeqNum = 0;
		
		try {
			String fileName = pvCollatInfoDto.getIdByCodes();
			String colFileName = null;

			colFileName = fileName + MPStatistic.COL_FILE_END_NAME;
			fileName += MPStatistic.OUTPUT;
			
			File file = basePath.resolve(fileName).toFile();
			File colFile = colBasePath.resolve(colFileName).toFile();
			
			if (!colFile.exists()) {
				printLog(ILogger.MODE_CHAR_ERROR, "Can't found file. (" + colFile + ")");
				return rootList;	
			}
			
			if (!file.exists()) {
				printLog(ILogger.MODE_CHAR_ERROR, "Can't found file. (" + file + ")");
				return rootList;
			}
			
			// 출력납입주기 조건 세팅 ex (납입주기, [1,12])
			Map<String, List<String>> conditionMap = new HashMap<>();
			if (EnUtility.isNotBlank(rateKeyOutDto.outputPaymentPeriod)) {
				for (String conditionValue : rateKeyOutDto.outputPaymentPeriod.trim().split(" *, *")) {
					if (EnUtility.isBlank(conditionValue)) {
						continue;
					}
					
					List<String> conditionValueList = conditionMap.get(MPStatistic.COL_PAYMENT_MODE);
					if (conditionValueList == null) {
						conditionValueList = new ArrayList<>();
						conditionMap.put(MPStatistic.COL_PAYMENT_MODE, conditionValueList);
					}
					conditionValueList.add(conditionValue);
				}
			}
			boolean isHorizontalPremByPaymentPeriod = false;
			{
				if (useMetaTypeMap.get("수평납기별보험료") != null
						&& useMetaTypeMap.get("수평납기별보험료").booleanValue()) {
					isHorizontalPremByPaymentPeriod = true;
				}
			}
			
			// <보험료종류, <납입주기, index>>
			Map<String, Map<String, Integer>> premIndexMapByPaymentPeriod = new HashMap<>();
			Map<String, List<String>> premByPaymentPeriodListMap = new LinkedHashMap<>();
			Map<String, Integer> premByIndexMap = new HashMap<>();
			// 출력 조건 세팅 및 '수평납기별보험료' 체크 ex (LRVD, [0]);
			// LRVD=0
			// P:LRVD=0,V:LRVD=1
			// AOD!=0&&LRVD!=0
			// && || () !
			String complexConditionExpression = null;
			if (EnUtility.isNotBlank(rateKeyOutDto.condition)) {
				String detailLayoutName = "";
				if (rateKeyOutDto.layoutName.indexOf("&") > -1) {
					String[] split_layoutName = rateKeyOutDto.layoutName.split("&");
					if (split_layoutName.length == 2) {
						detailLayoutName = split_layoutName[1];
					}
				}
				
				String[] detailLayoutConditions = rateKeyOutDto.condition.trim().split(" *, *");
				for (String detailLayoutCondition : detailLayoutConditions) {
					if (detailLayoutCondition.indexOf(":") > -1) {
						String[] split_detailLayoutCondition = detailLayoutCondition.split(" *: *", -1);
						if (split_detailLayoutCondition.length != 2) {
							continue;
						}
						if (!split_detailLayoutCondition[0].equals(detailLayoutName)) {
							continue;
						}
						
						detailLayoutCondition = split_detailLayoutCondition[1];
					}
					
					if (containsLogicalOperators(detailLayoutCondition)) {
						complexConditionExpression = detailLayoutCondition;
					} else {
						for (String condition : detailLayoutCondition.trim().split(" *\\|\\| *")) {
							if (EnUtility.isBlank(condition)) {
								continue;
							}
							
							if ("수평납기별보험료".equals(condition)) {
								isHorizontalPremByPaymentPeriod = true;
								continue;
							}
							
							String[] split_condition = condition.split(" *= *", -1);
							if (split_condition.length < 2) {
								continue;
							}
							
							List<String> conditionValueList = conditionMap.get(split_condition[0]);
							if (conditionValueList == null) {
								conditionValueList = new ArrayList<>();
								conditionMap.put(split_condition[0], conditionValueList);
							}
							conditionValueList.add(split_condition[1]);
						}
					}
//					break;
				}
			}
			
			Field[] fields = PvCollatInfoDto.class.getDeclaredFields();
			
			// 레이아웃 형태 변환
			// ex) [[상품코드, 담보코드], [실보험기간, 실납입기간], [보험료]]
			// 세부 입력 형태
			//     일반1형 : 산출 컬럼 명칭 ex) 납입주기
			//     일반2형 : 담보 컬럼 명칭 (PvCollatInfoDto 필드명) ex) 출력용개시일, 판매시작일, applyStartData
			//     일반3형 : 사업비 컬럼 명칭 (NewPvBaseDbDto.VALUE_FIELD) ex) a1, b1, ce, r1
			// --
			//     조건1형 : 산출 컬럼이 미존재하는 경우 타 컬럼값 출력 ex) SC_SA기시V ? SC_SA기시V : SA기시V
			//     조건2형 : 산출 컬럼의 값에 조건을 추가하여 출력하는 값 또는 컬럼 변경 ex) 납입주기==0 ? 0 : SA순P
			// --
			//     종합1형 : 위 조건형의 중첩 형태
			List<List<String>> outList = new ArrayList<>();
			outList.add(strCastList(rateKeyOutDto.out1));
			outList.add(strCastList(rateKeyOutDto.out2));
			outList.add(strCastList(rateKeyOutDto.out3));
			outList.add(strCastList(rateKeyOutDto.out4));
			outList.add(strCastList(rateKeyOutDto.out5));
			outList.add(strCastList(rateKeyOutDto.out6));
			outList.add(strCastList(rateKeyOutDto.out7));
			outList.add(strCastList(rateKeyOutDto.out8));
			outList.add(strCastList(rateKeyOutDto.out9));
			outList.add(strCastList(rateKeyOutDto.out10));
			outList.add(strCastList(rateKeyOutDto.out11));
			outList.add(strCastList(rateKeyOutDto.out12));
			outList.add(strCastList(rateKeyOutDto.out13));
			outList.add(strCastList(rateKeyOutDto.out14));
			outList.add(strCastList(rateKeyOutDto.out15));
			outList.add(strCastList(rateKeyOutDto.out16));
			outList.add(strCastList(rateKeyOutDto.out17));
			outList.add(strCastList(rateKeyOutDto.out18));
			outList.add(strCastList(rateKeyOutDto.out19));
			outList.add(strCastList(rateKeyOutDto.out20));
			outList.add(strCastList(rateKeyOutDto.out21));
			outList.add(strCastList(rateKeyOutDto.out22));
			outList.add(strCastList(rateKeyOutDto.out23));
			outList.add(strCastList(rateKeyOutDto.out24));
			outList.add(strCastList(rateKeyOutDto.out25));
			outList.add(strCastList(rateKeyOutDto.out26));
			outList.add(strCastList(rateKeyOutDto.out27));
			outList.add(strCastList(rateKeyOutDto.out28));
			outList.add(strCastList(rateKeyOutDto.out29));
			outList.add(strCastList(rateKeyOutDto.out30));
			outList.add(strCastList(rateKeyOutDto.out31));
			outList.add(strCastList(rateKeyOutDto.out32));
			outList.add(strCastList(rateKeyOutDto.out33));
			outList.add(strCastList(rateKeyOutDto.out34));
			outList.add(strCastList(rateKeyOutDto.out35));
			outList.add(strCastList(rateKeyOutDto.out36));
			outList.add(strCastList(rateKeyOutDto.out37));
			outList.add(strCastList(rateKeyOutDto.out38));
			outList.add(strCastList(rateKeyOutDto.out39));
			outList.add(strCastList(rateKeyOutDto.out40));
			outList.add(strCastList(rateKeyOutDto.out41));
			outList.add(strCastList(rateKeyOutDto.out42));
			outList.add(strCastList(rateKeyOutDto.out43));
			outList.add(strCastList(rateKeyOutDto.out44));
			outList.add(strCastList(rateKeyOutDto.out45));
			outList.add(strCastList(rateKeyOutDto.out46));
			outList.add(strCastList(rateKeyOutDto.out47));
			outList.add(strCastList(rateKeyOutDto.out48));
			outList.add(strCastList(rateKeyOutDto.out49));
			outList.add(strCastList(rateKeyOutDto.out50));
			
			// 사업비정보 컬럼 출력 여부
			boolean hasBizExpCol = false;
			List<String> bizExpValueFieldList = Arrays.asList(BizExpenseDto.VALUE_FIELD);
			List<String> bizExpKeyFieldLabelList = Arrays.asList(BizExpenseDto.getKeyFieldLabel());
			
			// 위 레이아웃 형태와 실제 컬럼파일에 위치 값을 읽어와 변환되어 입력되어야할 index형태를 생성, -1 로 초기 설정
			// ex) [[1, 2], [8, 9], [12], [-1]]
			List<List<Integer>> convertIdx = new ArrayList<>(Collections.nCopies(50, null));
			for (int i = 0; i < outList.size(); i++) {
				List<String> _outList = outList.get(i);
				convertIdx.set(i, new ArrayList<>(Collections.nCopies(Math.max(_outList.size(), 1), -1)));
				
				if (_outList.size() == 1 && !EnUtility.isBlank(_outList.get(0))) {
					String colName = _outList.get(0).replace(" ", "");
					if (colName.equals(MPStatistic.COL_V_YEAR)) {
						horizontalArrayIdxMap.put(MPStatistic.COL_V_YEAR, i);
					} else if (colName.equals(MPStatistic.COL_SURRENDER)) {
						horizontalArrayIdxMap.put(MPStatistic.COL_SURRENDER, i);
					} else if (colName.contains(MPStatistic.COL_V_YEAR_START)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains(MPStatistic.COL_V_YEAR_END)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains(MPStatistic.COL_SURRENDER_START)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains(MPStatistic.COL_SURRENDER_END)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains("표준" + MPStatistic.COL_V_YEAR_START)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains("표준" + MPStatistic.COL_V_YEAR_END)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains("표준" + MPStatistic.COL_SURRENDER_START)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains("표준" + MPStatistic.COL_SURRENDER_END)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains(MPStatistic.COL_RISK_PREM_YEAR)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.contains(MPStatistic.COL_RISK_PREM)) {
						arrayValueColumnIndex_afterConvert.add(i);
					} else if (colName.startsWith("t")) {
						verticalSqlIdx = i;
						addSeqNum = getAddSeqNum(colName);
					} else if (bizExpValueFieldList.contains(colName)) {
						hasBizExpCol = true;
					}
					
					else if (MPStatistic.COL_BSNS_PREM_.equals(colName)) { // 연납보험료
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_MONTH_BSNS_PREM_, "1", i);
					} else if ("반기납영업보험료".equals(colName)) {
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_MONTH_BSNS_PREM_, "2", i);
					} else if ("분기납영업보험료".equals(colName)) {
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_MONTH_BSNS_PREM_, "4", i);
					} else if (MPStatistic.COL_MONTH_BSNS_PREM_.equals(colName)) { // 영업보험료
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_MONTH_BSNS_PREM_, "12", i);
					}
					
					else if (MPStatistic.COL_BSNS_PREM.equals(colName)) { // SA연납보험료
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_MONTH_BSNS_PREM, "1", i);
					} else if ("SA반기납영업보험료".equals(colName)) {
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_MONTH_BSNS_PREM, "2", i);
					} else if ("SA분기납영업보험료".equals(colName)) {
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_MONTH_BSNS_PREM, "4", i);
					} else if (MPStatistic.COL_MONTH_BSNS_PREM.equals(colName)) { // SA영업보험료
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_MONTH_BSNS_PREM, "12", i);
					}
					
					else if (MPStatistic.COL_YEAR_NET_PREM.equals(colName)) { // 연납순보험료
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_NET_PREM, "1", i);
					} else if ("반기납순보험료".equals(colName)) {
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_NET_PREM, "2", i);
					} else if ("분기납순보험료".equals(colName)) {
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_NET_PREM, "4", i);
					} else if (MPStatistic.COL_NET_PREM.equals(colName)) { // 순보험료
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_NET_PREM, "12", i);
					}
					
					else if (MPStatistic.COL_YEAR_NET_PREM_SA.equals(colName)) { // SA연납순보험료
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_NET_PREM_SA, "1", i);
					} else if ("SA반기납순보험료".equals(colName)) {
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_NET_PREM_SA, "2", i);
					} else if ("SA분기납순보험료".equals(colName)) {
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_NET_PREM_SA, "4", i);
					} else if (MPStatistic.COL_NET_PREM_SA.equals(colName)) { // SA순보험료
						putPremIndexMapByPaymentPeriod(premIndexMapByPaymentPeriod, MPStatistic.COL_NET_PREM_SA, "12", i);
					}
				}
			}
			
			// <colName, index>
			Map<String, Integer> colNameIdx = new HashMap<>();
			try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(colFile), StandardCharsets.UTF_8))) {
				String line;
				while ((line = br.readLine()) != null) {
					if (hasBizExpCol) {
						line += "," + String.join(",", bizExpValueFieldList);
					}
					
					String[] colNames = line.replace(" ", "").split(",");
					
					for(int i = 0; i < colNames.length; i++) {
						String colName = colNames[i];
						
						colNameIdx.put(colName, i);
						
						if (colName.equals(MPStatistic.COL_V_YEAR_START)) {
							arrayIdxMap.put(MPStatistic.COL_V_YEAR_START, i);
						} else if (colName.equals(MPStatistic.COL_V_YEAR_END)) {
							arrayIdxMap.put(MPStatistic.COL_V_YEAR_END, i);
						} else if (colName.equals(MPStatistic.COL_SURRENDER_START)) {
							arrayIdxMap.put(MPStatistic.COL_SURRENDER_START, i);
						} else if (colName.equals(MPStatistic.COL_SURRENDER_END)) {
							arrayIdxMap.put(MPStatistic.COL_SURRENDER_END, i);
						} else if (colName.equals(MPStatistic.COL_RISK_PREM_YEAR)) {
							arrayIdxMap.put(MPStatistic.COL_RISK_PREM_YEAR, i);
						} else if (colName.equals(MPStatistic.COL_RISK_PREM)) {
							arrayIdxMap.put(MPStatistic.COL_RISK_PREM, i);
						}
						
						else if (MPStatistic.COL_MONTH_BSNS_PREM_.equals(colName)) { // 영업보험료
							premByIndexMap.put(MPStatistic.COL_MONTH_BSNS_PREM_, i);
						} else if (MPStatistic.COL_MONTH_BSNS_PREM.equals(colName)) { // SA영업보험료
							premByIndexMap.put(MPStatistic.COL_MONTH_BSNS_PREM, i);
						} else if (MPStatistic.COL_NET_PREM.equals(colName)) { // 순보험료
							premByIndexMap.put(MPStatistic.COL_NET_PREM, i);
						} else if (MPStatistic.COL_NET_PREM_SA.equals(colName)) { // SA순보험료
							premByIndexMap.put(MPStatistic.COL_NET_PREM_SA, i);
						}
						
						if (bizExpKeyFieldLabelList.contains(colName)) {
							bizExpKeyIdxMap.put(colName, i);
						}

						for (int j = 0; j < outList.size(); j++) {
							List<String> _outList = outList.get(j);
							if (_outList.contains(colName)) {
								convertIdx.get(j).set(_outList.indexOf(colName), i);
							}
						}
					}
					
					if (colNameIdx.size() > 0) {
						MetaKeyValueDto[] metaKeyValueDto = getMetaKeyValue(" AND (MPMV.CONV_ITM_CD = '" + String.join("' OR MPMV.CONV_ITM_CD = '", colNameIdx.keySet()) + "')");
						metaKeyDataConv = Arrays.stream(metaKeyValueDto)
							.collect(Collectors.groupingBy(
									MetaKeyValueDto::getConvItmCd,
									Collectors.toMap(
											MetaKeyValueDto::getConvItmValue,
											
											MetaKeyDto.OUT_CODE_IT.equals(rateKeyOutDto.outCodeType) ? 
													MetaKeyValueDto::getCompanyCd :
											MetaKeyDto.OUT_CODE_KIDI.equals(rateKeyOutDto.outCodeType) ?
													MetaKeyValueDto::getKidiCd :
											MetaKeyDto.OUT_CODE_ACT.equals(rateKeyOutDto.outCodeType) ?
													MetaKeyValueDto::getActFirmCd :
											MetaKeyValueDto::getConvItmValue,
											
											(v1, v2) -> v2
									)
							));
					}
					
					break;
				}
			}
			
			List<List<RateKeyOutFrmtDto>> outFrmtList = new ArrayList<>();
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt1));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt2));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt3));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt4));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt5));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt6));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt7));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt8));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt9));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt10));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt11));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt12));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt13));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt14));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt15));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt16));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt17));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt18));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt19));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt20));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt21));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt22));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt23));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt24));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt25));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt26));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt27));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt28));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt29));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt30));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt31));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt32));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt33));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt34));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt35));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt36));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt37));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt38));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt39));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt40));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt41));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt42));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt43));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt44));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt45));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt46));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt47));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt48));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt49));
			outFrmtList.add(strCastFrmtDtoList(rateKeyOutDto.outFrmt50));
			
			Map<String, BizExpenseDto> bizExpMap = new HashMap<>();
			if (hasBizExpCol) {
				for (BizExpenseDto bizExpDto : bizExpDtos) {
					String[] bizExpKey = new String[BizExpenseDto.KEY_FIELD.length];
					for (int i = 0; i < BizExpenseDto.KEY_FIELD.length; i++) {
						if (colNameIdx.get(BizExpenseDto.KEY_FIELD[i][0]) != null) {
							bizExpKey[i] = (String)EnBeanUtility.getFieldValue(bizExpDto, BizExpenseDto.KEY_FIELD[i][1]);
						}
					}
					bizExpMap.put(String.join(MPStatistic.UNBER_BAR, bizExpKey), bizExpDto);
				}
			}
			
//			System.out.println("conditionMap=" + conditionMap);
//			System.out.println("colNameIdxMap=" + colNameIdx);
			
			Set<Entry<String, List<String>>> conditionSet = conditionMap.entrySet();
			try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
				String line;
				String[] bizExpKeyFieldLabels = BizExpenseDto.getKeyFieldLabel();
				boolean failedToPassCondition = false;
				String prevBogiNapgiKey = null;
				
				while ((line = br.readLine()) != null) {
					List<String> list = new ArrayList<>(Collections.nCopies(50, ""));
					String[] col = line.trim().split(" *, *");

					if (hasBizExpCol) {
						String[] bizExpKey = new String[BizExpenseDto.KEY_FIELD.length];
						for (int i = 0; i < bizExpKeyFieldLabels.length; i++) {
							Integer bizExpKeyIdx = bizExpKeyIdxMap.get(bizExpKeyFieldLabels[i]);
							String _keyColData = null; 
							if (bizExpKeyIdx != null) {
								_keyColData = col[bizExpKeyIdx]; 
							}
							bizExpKey[i] = _keyColData;
						}
						
						BizExpenseDto bizExpDto = bizExpMap.get(String.join(MPStatistic.UNBER_BAR, bizExpKey));
						if (bizExpDto != null) {
							col = (line + "," + bizExpDto.getBizExpValue()).trim().split(" *, *");
						}
					}
					
					if (complexConditionExpression != null) {
						try {
							if (!evaluateFilterCondition(complexConditionExpression, col, colNameIdx)) {
								failedToPassCondition = true;
							}
						} catch (Exception e) {
							printLog('E', "복잡한 조건 평가 실패: " + complexConditionExpression + ", " + e.getMessage());
							failedToPassCondition = true;
						}
					} else {
						for (Entry<String, List<String>> entry : conditionSet) {
							String colName = entry.getKey();
							List<String> conditValues = entry.getValue();
							
							// MP컬럼 목록에 필터링 조건에 해당하는 컬럼이 존재하지 않으면 무시한다.
							if (!colNameIdx.containsKey(colName)) {
								continue;
							}
							
							boolean isSame = false;
							for (String conditValue : conditValues) {
								int colIndex = colNameIdx.get(colName);
								
								if (col[colIndex].equals(conditValue)) {
									isSame = true;
									break;
								}
							}
							
							if (isSame) {
								continue;
							}
							
							failedToPassCondition = true;
							break;
						}
					}
					
					if (failedToPassCondition) {
						failedToPassCondition = false;
						continue;
					}
					
//					int amount = 1;
//					try {
//						if (amountIdx != -1) {
//							amount = Integer.parseInt(col[amountIdx]);
//						}
//					} catch (Exception e) {
//						e.printStackTrace();
//					}
					
					for (int i = 0; i < convertIdx.size(); i++) {
						List<Integer> _converIdx = convertIdx.get(i);
						List<RateKeyOutFrmtDto> _outFrmtList = outFrmtList.get(i);
						
						boolean isHIdx = !_isVerticalArray && horizontalArrayIdxMap.size() > 0 && horizontalArrayIdxMap.values().contains(i);
						
						String v = "";
						if (_converIdx == null || (_converIdx.size() == 1 && _converIdx.get(0) == -1)) {
							if (outList.size() > i 
									&& outList.get(i) != null 
									&& outList.get(i).size() > 0
									&& outList.get(i).get(0) != null) {
								v = getConvValue(outList.get(i).get(0), fields, pvCollatInfoDto, col, colNameIdx);
							} else {
								v = "";
							}
							if (!arrayValueColumnIndex_afterConvert.contains(i)) {
								v = applyFormat(_outFrmtList, v, 0);
							}
							if (EnUtility.isBlank(v) && !isHIdx) 
								continue;
						} else if (_converIdx.size() > 0) {
							for (int j = 0; j < _converIdx.size(); j++) {
								String outName = outList.get(i).get(j);
								Map<String, String> meteKeyValueConvData = metaKeyDataConv.get(outName);
//								boolean mulAmount = outName.indexOf("*기준가입금액") > -1;
								
								Integer _convIdx = _converIdx.get(j);
								String tempV = "";
								if (_convIdx != -1 && col.length > _convIdx && !EnUtility.isBlank(col[_convIdx])) {
									tempV = col[_convIdx];
									
									if (meteKeyValueConvData != null
											&& meteKeyValueConvData.get(tempV) != null) {
										tempV = meteKeyValueConvData.get(tempV);
									}
								} else {
									if (outList.size() > i
											&& outList.get(i) != null 
											&& outList.get(i).size() > j 
											&& outList.get(i).get(j) != null) {											
										tempV = getConvValue(outList.get(i).get(j), fields, pvCollatInfoDto, col, colNameIdx);
									}
								}
								tempV = tempV.trim();
								
								if (!arrayValueColumnIndex_afterConvert.contains(i)) {
									tempV = applyFormat(_outFrmtList, tempV, j);
								}
								
								v += tempV;
							}
						}
						
						if (isHIdx) {
							for (Entry<String, Integer> entry : horizontalArrayIdxMap.entrySet()) {
								if (entry.getValue() != i) {
									continue;
								}
								
								if (MPStatistic.COL_V_YEAR.equals(entry.getKey())) {
									Integer startIdx = arrayIdxMap.get(MPStatistic.COL_V_YEAR_START);
									Integer endIdx = arrayIdxMap.get(MPStatistic.COL_V_YEAR_END);

									if (startIdx != null
											&& col.length > startIdx
											&& !EnUtility.isBlank(col[startIdx])
											&& endIdx != null
											&& col.length > endIdx
											&& !EnUtility.isBlank(col[endIdx])) {
										String startStr = col[startIdx];
										String endStr = col[endIdx];
										
										int lastT = startStr.lastIndexOf("|") > -1 ? 
												Integer.parseInt(startStr.substring(startStr.lastIndexOf("|") + 1, startStr.lastIndexOf("="))) + 1
												: 1;
										v = startStr.substring(0, startStr.length() - 1) + "|" + lastT + endStr.substring(endStr.lastIndexOf("="));
									}
								} else if (MPStatistic.COL_SURRENDER.equals(entry.getKey())) {
									Integer startIdx = arrayIdxMap.get(MPStatistic.COL_SURRENDER_START);
									Integer endIdx = arrayIdxMap.get(MPStatistic.COL_SURRENDER_END);

									if (startIdx != null
											&& col.length > startIdx
											&& !EnUtility.isBlank(col[startIdx])
											&& endIdx != null
											&& col.length > endIdx
											&& !EnUtility.isBlank(col[endIdx])) {
										String startStr = col[startIdx];
										String endStr = col[endIdx];
										
										int lastT = Integer.parseInt(startStr.substring(startStr.lastIndexOf("|") + 1, startStr.lastIndexOf("="))) + 1;
										v = startStr.substring(0, startStr.length() - 1) + "|" + lastT + endStr.substring(endStr.lastIndexOf("="));
									}
								}
								
								if (_isHorizontalSplitArray) {
									String[] valueArr = v.substring(1, v.length() - 1).split("\\|");
									
									if (valueArr.length > 0) {
										v = applyFormat(_outFrmtList, valueArr[0].split("=")[1], 0);
												
										for (int vi = 1; vi < valueArr.length; vi++) {
											String value = applyFormat(_outFrmtList, valueArr[vi].split("=")[1], 0);
											if (vi + i > list.size() - 1) {
												list.add(value);
											} else {
												list.set(vi + i, value);
											}
										}
									}
								}
								
								break;
							}
						}
						
						list.set(i, v);
					}
					
//					System.out.println("#VerticalArrayIdx=" + verticalArrayIdx);
//					System.out.println("#ValueList.size=" + list.size());
//					System.out.println("#ValueList=" + list);
					
					if (isHorizontalPremByPaymentPeriod) {
						String[] keyCol = new String[colNameIdx.get(MPStatistic.COL_AGE) + 1];
						System.arraycopy(col, 0, keyCol , 0, colNameIdx.get(MPStatistic.COL_AGE) + 1);
						keyCol[colNameIdx.get(MPStatistic.COL_PAYMENT_MODE)] = ""; // 납입주기 제거
						String key = String.join("_", keyCol);
						
						List<String> _list = premByPaymentPeriodListMap.get(key);
						if (_list == null) {
							_list = list;
							premByPaymentPeriodListMap.put(key, _list);
						}
						
						String paymentPeriod = col[colNameIdx.get(MPStatistic.COL_PAYMENT_MODE)];
						for (Entry<String, Integer> entry : premByIndexMap.entrySet()) {
							Map<String, Integer> paymentPeriodMap = premIndexMapByPaymentPeriod.get(entry.getKey());
							if (paymentPeriodMap == null)
								continue;
							
							Integer premIndex = paymentPeriodMap.get(paymentPeriod);
							if (premIndex == null)
								continue;
							
							String prem = col[entry.getValue()];
							prem = applyFormat(outFrmtList.get(premIndex), prem, 0);
							_list.set(premIndex, prem);
						}
						
						String bogiNapgiKey = String.join("_",
								col[colNameIdx.get(MPStatistic.COL_NEW_RENEWAL)],
								col[colNameIdx.get(MPStatistic.COL_MATURITY_TYPE)],
								col[colNameIdx.get(MPStatistic.COL_MATURITY)],
								col[colNameIdx.get(MPStatistic.COL_PAYMENT_TYPE)],
								col[colNameIdx.get(MPStatistic.COL_PAYMENT)]);
						
						if (prevBogiNapgiKey != null && !bogiNapgiKey.equals(prevBogiNapgiKey)) {
							for (Entry<String, List<String>> entry : premByPaymentPeriodListMap.entrySet()) {
								rootList.add(entry.getValue().toArray(new String[entry.getValue().size()]));
							}
							premByPaymentPeriodListMap.clear();
						}
						
						prevBogiNapgiKey = bogiNapgiKey;
						
						continue;
					}
					
					if (_isVerticalArray && arrayValueColumnIndex_afterConvert.size() > 0) {
						// lineNum, <index, value>
						LinkedHashMap<Integer, HashMap<Integer, String>> tempMap = new LinkedHashMap<>();
						
						for (Integer vIdx : arrayValueColumnIndex_afterConvert) {
							List<RateKeyOutFrmtDto> _outFrmtList = outFrmtList.get(vIdx);
							
							String colValue = list.get(vIdx);
							if (EnUtility.isNotBlank(colValue) && colValue.length() > 2 && colValue.startsWith("[") && colValue.endsWith("]")) {
								for (String _v : colValue.substring(1, colValue.length() - 1).split("\\|")) {
									String[] keyValue = _v.split("=", -1);
									
									if (keyValue.length != 2) {
										continue;
									}
									
									// 배열 요소값에 사칙연산 적용 (포맷 적용 전)
									keyValue[1] = processArrayElementValue(keyValue[1], fields, pvCollatInfoDto, col, colNameIdx);
									
									keyValue[1] = applyFormat(_outFrmtList, keyValue[1], 0);
									
									Integer lineNum = Integer.parseInt(keyValue[0]);
									
									HashMap<Integer, String> _map = tempMap.get(lineNum);
									if (_map == null) {
										_map = new HashMap<>();
										tempMap.put(lineNum, _map);
									}
									_map.put(vIdx, keyValue[1]);
								}
							} else {
//								System.out.println("# No Vertical Processing (vIdx=" + vIdx + ", value=" + colValue + ")");
							}
						}
						
						for (Entry<Integer, HashMap<Integer, String>> entry : tempMap.entrySet()) {
							for (Entry<Integer, String> _entry : entry.getValue().entrySet()) {
								if (verticalSqlIdx != -1) {
									list.set(verticalSqlIdx, String.valueOf(entry.getKey() + addSeqNum));
									list.set(_entry.getKey(), _entry.getValue());
								} else {
									list.set(_entry.getKey(), String.valueOf(entry.getKey()));
									list.set(_entry.getKey() + 1, _entry.getValue());
								}
							}

							// 배열 수직 변환 후 t값 기반 삼항연산자 재평가
							String[] rowData = list.toArray(new String[list.size()]);
							rowData = reprocessConditionalExpressionsWithT(rowData, fields, pvCollatInfoDto, colNameIdx, convertIdx, outList);

							rootList.add(rowData);
						}
					} else {
						rootList.add(list.toArray(new String[list.size()]));
					}
				}
			}
			
			for (Entry<String, List<String>> entry : premByPaymentPeriodListMap.entrySet()) {
				rootList.add(entry.getValue().toArray(new String[entry.getValue().size()]));
			}
		} catch (Exception e) {
			String stackTrace = e.toString() + " " + String.join(" ", Arrays.stream(e.getStackTrace()).map(StackTraceElement::toString).toArray(String[]::new));
			System.out.println(stackTrace);
		}
		
		System.out.println("작업시간:" + (System.currentTimeMillis() - time));
		
		return rootList;
	}
	
	private void putPremIndexMapByPaymentPeriod(Map<String, Map<String, Integer>> premMap, String premName, String paymentPeriod, int index) {
		Map<String, Integer> paymentPeriodMap = premMap.get(premName);
		if (paymentPeriodMap == null) {
			paymentPeriodMap = new HashMap<>();
			premMap.put(premName, paymentPeriodMap);
		}
		
		paymentPeriodMap.put(paymentPeriod, index);
	}
	
	protected abstract Path getOutputFileBasePath(String homePath);
	
	protected abstract Path getColumnFileBasePath(String homePath);
	
	protected abstract MetaKeyValueDto[] getMetaKeyValue(String where) throws Exception;
	
	/**
	 * @param type /Common/common/kr/dazzle/log/ILogger.java
	 * @param message
	 */
	protected void printLog(char mode, String msg) {
		System.out.println(msg);
	}
	
	private List<String> strCastList(String str) {
		return strCastList(str, "&");
	}
	
	private List<String> strCastList(String str, String delimiter) {
		if (EnUtility.isBlank(str)) {
			return new ArrayList<>();
		}
		
		return Arrays.asList(str.replace(" ", "").split(delimiter));
	}
	
	private int getAddSeqNum(String colName) {
		int num = 0;
		
		if (colName.length() > 1) {
			try {
				num = Integer.parseInt(colName.substring(1));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
		return num;
	}
	
	private List<RateKeyOutFrmtDto> strCastFrmtDtoList(String str) {
		List<RateKeyOutFrmtDto> list = new ArrayList<>();
		
		if (!EnUtility.isBlank(str)) {
			List<String> strList = Arrays.asList(str.replace(" ", "").split("&", -1));
			for (String outFrmt : strList) {
				list.add(new RateKeyOutFrmtDto(outFrmt));
			}
		}
		
		return list;
	}
	
	private String getConvValue(String str, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		String v = "";
		if (str != null) {
			// 배열 패턴 직접 처리 (최우선)
			if (containsArrayPattern(str)) {
				v = processArrayExpression(str, fields, dto, currentRowData, colNameIdx);
			}
			// 조건부 값 변환 로직 처리 (삼항연산자 형태)
			else if (str.contains("?") && str.contains(":")) {
				v = processConditionalValue(str, fields, dto, currentRowData, colNameIdx);
			} else if (containsArithmeticOperators(str)) {
				v = processArithmeticExpression(str, fields, dto, currentRowData, colNameIdx);
			} else if (EnUtility.isNumber(str)) {
				v = str;
			} else if (EnUtility.isBlank(str.replaceAll("[0-9,]+", ""))) {
				v = str;
			} else {
				if (str.indexOf(":") > -1) {
					str = str.substring(str.indexOf(":") + 1);
				}

				str = getLabelToName(str);
				for (Field field : fields) {
					if (field.getName().equals(str)) {
						v = (String) field.get(dto);
						break;
					}
				}
			}
		}

		return v;
	}
	
	/**
	 * 조건부 값 변환 로직 처리 (중첩 조건 지원)
	 * 형태: "조건 ? 참값 : 거짓값"
	 * 조건1형: SC_SA기시V ? SC_SA기시V : SA기시V (컬럼 존재 여부)
	 * 조건2형: 납입주기==0 ? 0 : SA순P (값 비교)
	 * 중첩형: a == 1 && b == 2 ? a : a == 2 && b == 2 ? b : c
	 */
	private String processConditionalValue(String conditionalStr, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		try {
			return parseConditionalExpression(conditionalStr.trim(), fields, dto, currentRowData, colNameIdx);
		} catch (Exception e) {
			printLog(ILogger.MODE_CHAR_ERROR, "조건부 값 변환 처리 중 오류: " + conditionalStr + ", " + e.getMessage());
			return conditionalStr; // 오류시 원본 반환
		}
	}

	/**
	 * 재귀적 조건식 파싱 (중첩 삼항연산자 지원)
	 * 예: a == 1 && b == 2 ? a : a == 2 && b == 2 ? b : c
	 */
	private String parseConditionalExpression(String expression, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		expression = expression.trim();

		// 삼항연산자가 없으면 단순 값으로 처리
		if (!expression.contains("?")) {
			return getFieldValue(expression, fields, dto, currentRowData, colNameIdx);
		}

		// 첫 번째 ? 위치 찾기
		int questionPos = findFirstQuestionMark(expression);
		if (questionPos == -1) {
			return getFieldValue(expression, fields, dto, currentRowData, colNameIdx);
		}

		String condition = expression.substring(0, questionPos).trim();
		String afterQuestion = expression.substring(questionPos + 1).trim();

		// : 위치 찾기 (중첩 고려)
		int colonPos = findMatchingColon(afterQuestion);
		if (colonPos == -1) {
			return expression; // 파싱 실패시 원본 반환
		}

		String trueValue = afterQuestion.substring(0, colonPos).trim();
		String falseValue = afterQuestion.substring(colonPos + 1).trim();

		// 조건 평가
		boolean conditionResult = evaluateComplexCondition(condition, fields, dto, currentRowData, colNameIdx);

		// 조건 결과에 따라 재귀적으로 처리
		String resultExpression = conditionResult ? trueValue : falseValue;
		
		// 결과 표현식이 사칙연산을 포함하는지 체크하고 처리
		if (containsArithmeticOperatorsOnly(resultExpression)) {
			return processArithmeticExpression(resultExpression, fields, dto, currentRowData, colNameIdx);
		} else {
			return parseConditionalExpression(resultExpression, fields, dto, currentRowData, colNameIdx);
		}
	}

	/**
	 * 첫 번째 ? 위치 찾기 (괄호 고려)
	 */
	private int findFirstQuestionMark(String expression) {
		int depth = 0;
		for (int i = 0; i < expression.length(); i++) {
			char c = expression.charAt(i);
			if (c == '(') {
				depth++;
			} else if (c == ')') {
				depth--;
			} else if (c == '?' && depth == 0) {
				return i;
			}
		}
		return -1;
	}

	/**
	 * 매칭되는 : 위치 찾기 (중첩된 삼항연산자 고려)
	 */
	private int findMatchingColon(String expression) {
		int depth = 0;
		int questionCount = 0;

		for (int i = 0; i < expression.length(); i++) {
			char c = expression.charAt(i);
			if (c == '(') {
				depth++;
			} else if (c == ')') {
				depth--;
			} else if (depth == 0) {
				if (c == '?') {
					questionCount++;
				} else if (c == ':') {
					if (questionCount == 0) {
						return i; // 첫 번째 삼항연산자의 : 찾음
					} else {
						questionCount--; // 중첩된 삼항연산자의 :
					}
				}
			}
		}
		return -1;
	}

	/**
	 * 복잡한 조건 평가 (논리 연산자 지원)
	 * 예: a == 1 && b == 2, c != 3 || d > 5
	 */
	private boolean evaluateComplexCondition(String condition, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		condition = condition.trim();

		// OR 연산자 처리 (||)
		if (condition.contains("||")) {
			String[] orParts = splitByOperator(condition, "||");
			for (String orPart : orParts) {
				if (evaluateComplexCondition(orPart.trim(), fields, dto, currentRowData, colNameIdx)) {
					return true; // 하나라도 true면 전체 true
				}
			}
			return false;
		}

		// AND 연산자 처리 (&&)
		if (condition.contains("&&")) {
			String[] andParts = splitByOperator(condition, "&&");
			for (String andPart : andParts) {
				if (!evaluateComplexCondition(andPart.trim(), fields, dto, currentRowData, colNameIdx)) {
					return false; // 하나라도 false면 전체 false
				}
			}
			return true;
		}

		// 단일 조건 처리
		return evaluateSimpleCondition(condition, fields, dto, currentRowData, colNameIdx);
	}

	/**
	 * 연산자로 문자열 분리 (괄호 고려)
	 */
	private String[] splitByOperator(String expression, String operator) {
		List<String> parts = new ArrayList<>();
		int depth = 0;
		int start = 0;

		for (int i = 0; i <= expression.length() - operator.length(); i++) {
			char c = expression.charAt(i);
			if (c == '(') {
				depth++;
			} else if (c == ')') {
				depth--;
			} else if (depth == 0 && expression.substring(i, i + operator.length()).equals(operator)) {
				parts.add(expression.substring(start, i));
				start = i + operator.length();
				i += operator.length() - 1; // 연산자 길이만큼 건너뛰기
			}
		}
		parts.add(expression.substring(start)); // 마지막 부분 추가

		return parts.toArray(new String[0]);
	}

	/**
	 * 단일 조건 평가 (기존 로직)
	 */
	private boolean evaluateSimpleCondition(String condition, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		// 특수 조건 처리
		if (condition.startsWith("EXISTS(") && condition.endsWith(")")) {
			// EXISTS(컬럼명) - 컬럼 파일에 해당 컬럼이 존재하는지 체크
			String colName = condition.substring(7, condition.length() - 1).trim();
			return colNameIdx.containsKey(colName);
		}

		// 비교 연산자 체크 (==, !=, >, <, >=, <=)
		if (condition.contains("==")) {
			String[] parts = condition.split("==", 2);
			String leftValue = getFieldValue(parts[0].trim(), fields, dto, currentRowData, colNameIdx);
			String rightValue = getFieldValue(parts[1].trim(), fields, dto, currentRowData, colNameIdx);
			return leftValue.equals(rightValue);
		} else if (condition.contains("!=")) {
			String[] parts = condition.split("!=", 2);
			String leftValue = getFieldValue(parts[0].trim(), fields, dto, currentRowData, colNameIdx);
			String rightValue = getFieldValue(parts[1].trim(), fields, dto, currentRowData, colNameIdx);
			return !leftValue.equals(rightValue);
		} else if (condition.contains(">=")) {
			String[] parts = condition.split(">=", 2);
			String leftValue = getFieldValue(parts[0].trim(), fields, dto, currentRowData, colNameIdx);
			String rightValue = getFieldValue(parts[1].trim(), fields, dto, currentRowData, colNameIdx);
			return compareNumeric(leftValue, rightValue) >= 0;
		} else if (condition.contains("<=")) {
			String[] parts = condition.split("<=", 2);
			String leftValue = getFieldValue(parts[0].trim(), fields, dto, currentRowData, colNameIdx);
			String rightValue = getFieldValue(parts[1].trim(), fields, dto, currentRowData, colNameIdx);
			return compareNumeric(leftValue, rightValue) <= 0;
		} else if (condition.contains(">")) {
			String[] parts = condition.split(">", 2);
			String leftValue = getFieldValue(parts[0].trim(), fields, dto, currentRowData, colNameIdx);
			String rightValue = getFieldValue(parts[1].trim(), fields, dto, currentRowData, colNameIdx);
			return compareNumeric(leftValue, rightValue) > 0;
		} else if (condition.contains("<")) {
			String[] parts = condition.split("<", 2);
			String leftValue = getFieldValue(parts[0].trim(), fields, dto, currentRowData, colNameIdx);
			String rightValue = getFieldValue(parts[1].trim(), fields, dto, currentRowData, colNameIdx);
			return compareNumeric(leftValue, rightValue) < 0;
		} else {
			// 단순 컬럼 존재 여부 및 빈값 체크
			String fieldValue = getFieldValue(condition, fields, dto, currentRowData, colNameIdx);
			return EnUtility.isNotBlank(fieldValue);
		}
	}

	/**
	 * 필드값 조회 (컬럼명 -> 실제값 변환)
	 * 우선순위: 1) PV파일 현재행 컬럼값 2) DTO 필드값 3) 리터럴값
	 */
	private String getFieldValue(String fieldName, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		// 숫자나 문자열 리터럴인 경우 그대로 반환
		if (EnUtility.isNumber(fieldName) || fieldName.startsWith("\"") || fieldName.startsWith("'")) {
			return fieldName.replaceAll("[\"\']", "");
		}

		// 1순위: PV파일의 현재 행에서 컬럼값 조회
		Integer colIndex = colNameIdx.get(fieldName);
		if (colIndex != null && currentRowData.length > colIndex) {
			String colValue = currentRowData[colIndex];
			if (colValue != null) {
				return colValue.trim();
			}
		}

		// 2순위: DTO에서 필드값 조회
		String actualFieldName = getLabelToName(fieldName);
		for (Field field : fields) {
			if (field.getName().equals(actualFieldName)) {
				String value = (String) field.get(dto);
				return value != null ? value : "";
			}
		}

		// 3순위: 빈 문자열 반환
		return "";
	}

	/**
	 * 숫자 비교 (문자열을 숫자로 변환하여 비교)
	 */
	private int compareNumeric(String left, String right) {
		try {
			double leftNum = Double.parseDouble(left);
			double rightNum = Double.parseDouble(right);
			return Double.compare(leftNum, rightNum);
		} catch (NumberFormatException e) {
			// 숫자가 아닌 경우 문자열 비교
			return left.compareTo(right);
		}
	}
	
	private String getLabelToName(String s) {
		for (String[] conv : PvCollatInfoDto.VAL_LABEL) {
			if (conv[1].equals(s)) {
				s = s.replace(conv[1], conv[0]);
				break;
			}
		}
		
		return s;
	}
	
	private String applyFormat(List<RateKeyOutFrmtDto> _outFrmtList, String v, int idx) throws Exception {
		if (_outFrmtList != null
				&& _outFrmtList.size() > idx) {
			RateKeyOutFrmtDto rateKeyOutFrmtDto = _outFrmtList.get(idx);
			
			v = rateKeyOutFrmtDto.applyFormat(v);
		}
		
		return v;
	}
	
	/**
	 * 사칙연산 연산자 포함 여부 체크 (EXISTS() 함수와 구분)
	 */
	private boolean containsArithmeticOperators(String expression) {
		if (expression == null || expression.trim().isEmpty()) {
			return false;
		}

		// 조건부 변환(삼항연산자)이 포함된 경우 사칙연산이 아님
		if (expression.contains("?") && expression.contains(":")) {
			return false;
		}

		// EXISTS() 함수 패턴 제거 후 체크
		String cleanExpression = removeExistsFunctions(expression);

		// 사칙연산 연산자 체크 (+, -, *, /, %, 괄호)
		return cleanExpression.contains("+") || cleanExpression.contains("-") ||
			   cleanExpression.contains("*") || cleanExpression.contains("/") ||
			   cleanExpression.contains("%") || cleanExpression.contains("(") || cleanExpression.contains(")");
	}

	/**
	 * EXISTS() 함수 패턴을 제거하여 순수 사칙연산 여부 판단
	 */
	private String removeExistsFunctions(String expression) {
		// EXISTS(컬럼명) 패턴을 모두 제거
		String result = expression;

		// EXISTS( 로 시작하는 부분 찾기
		int existsStart = result.indexOf("EXISTS(");
		while (existsStart != -1) {
			// 매칭되는 닫는 괄호 찾기
			int parenCount = 0;
			int i = existsStart + 7; // "EXISTS(" 다음부터

			while (i < result.length()) {
				char c = result.charAt(i);
				if (c == '(') {
					parenCount++;
				} else if (c == ')') {
					if (parenCount == 0) {
						// EXISTS() 함수 완료
						result = result.substring(0, existsStart) + "TRUE" + result.substring(i + 1);
						break;
					} else {
						parenCount--;
					}
				}
				i++;
			}

			// 다음 EXISTS 찾기
			existsStart = result.indexOf("EXISTS(");
		}

		return result;
	}
	
	/**
	 * 순수 사칙연산만 포함하는지 체크 (삼항연산자 제외)
	 */
	private boolean containsArithmeticOperatorsOnly(String expression) {
		if (expression == null || expression.trim().isEmpty()) {
			return false;
		}

		// 삼항연산자가 포함된 경우 순수 사칙연산이 아님
		if (expression.contains("?") || expression.contains(":")) {
			return false;
		}

		// EXISTS() 함수 패턴 제거 후 체크
		String cleanExpression = removeExistsFunctions(expression);

		// 사칙연산 연산자 체크 (+, -, *, /, %, 괄호)
		return cleanExpression.contains("+") || cleanExpression.contains("-") ||
			   cleanExpression.contains("*") || cleanExpression.contains("/") ||
			   cleanExpression.contains("%") || cleanExpression.contains("(") || cleanExpression.contains(")");
	}

	/**
	 * 사칙연산 표현식 처리
	 * 예: "기준가입금액 - beta3 * 기준가입금액", "컬럼A + 1000", "(컬럼A - beta3) * 1.1"
	 */
	private String processArithmeticExpression(String expression, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		try {
			// 표현식을 토큰으로 분리하고 계산
			double result = evaluateArithmeticExpression(expression, fields, dto, currentRowData, colNameIdx);

			// 결과가 정수인 경우 소수점 제거
			if (result == (long) result) {
				return String.valueOf((long) result);
			} else {
				return String.valueOf(result);
			}
		} catch (Exception e) {
			printLog(ILogger.MODE_CHAR_ERROR, "사칙연산 처리 중 오류: " + expression + ", " + e.getMessage());
			return expression; // 오류시 원본 반환
		}
	}

	/**
	 * 사칙연산 표현식 평가 (재귀적 파싱)
	 */
	private double evaluateArithmeticExpression(String expression, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		expression = expression.trim();

		// 괄호 처리
		while (expression.contains("(")) {
			int start = expression.lastIndexOf('(');
			int end = expression.indexOf(')', start);
			if (end == -1) {
				throw new IllegalArgumentException("괄호가 올바르게 닫히지 않음: " + expression);
			}

			String subExpression = expression.substring(start + 1, end);
			double subResult = evaluateArithmeticExpression(subExpression, fields, dto, currentRowData, colNameIdx);
			expression = expression.substring(0, start) + subResult + expression.substring(end + 1);
		}

		// 덧셈/뺄셈 처리 (낮은 우선순위)
		return evaluateAdditionSubtraction(expression, fields, dto, currentRowData, colNameIdx);
	}
	
	/**
	 * 덧셈/뺄셈 처리 (낮은 우선순위)
	 */
	private double evaluateAdditionSubtraction(String expression, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		// + 또는 - 연산자를 찾아서 분리 (오른쪽부터 처리)
		for (int i = expression.length() - 1; i >= 0; i--) {
			char c = expression.charAt(i);
			if ((c == '+' || c == '-') && i > 0) { // 첫 번째 문자는 부호일 수 있으므로 제외
				String left = expression.substring(0, i).trim();
				String right = expression.substring(i + 1).trim();

				double leftValue = evaluateMultiplicationDivision(left, fields, dto, currentRowData, colNameIdx);
				double rightValue = evaluateMultiplicationDivision(right, fields, dto, currentRowData, colNameIdx);

				if (c == '+') {
					return leftValue + rightValue;
				} else {
					return leftValue - rightValue;
				}
			}
		}

		// 덧셈/뺄셈이 없으면 곱셈/나눗셈 처리
		return evaluateMultiplicationDivision(expression, fields, dto, currentRowData, colNameIdx);
	}

	/**
	 * 곱셈/나눗셈/나머지 처리 (높은 우선순위)
	 */
	private double evaluateMultiplicationDivision(String expression, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		// *, /, % 연산자를 찾아서 분리 (오른쪽부터 처리)
		for (int i = expression.length() - 1; i >= 0; i--) {
			char c = expression.charAt(i);
			if (c == '*' || c == '/' || c == '%') {
				String left = expression.substring(0, i).trim();
				String right = expression.substring(i + 1).trim();

				double leftValue = evaluateMultiplicationDivision(left, fields, dto, currentRowData, colNameIdx);
				double rightValue = evaluateValue(right, fields, dto, currentRowData, colNameIdx);

				if (c == '*') {
					return leftValue * rightValue;
				} else if (c == '/') {
					if (rightValue == 0) {
						throw new ArithmeticException("0으로 나눌 수 없습니다: " + expression);
					}
					return leftValue / rightValue;
				} else { // %
					return leftValue % rightValue;
				}
			}
		}

		// 곱셈/나눗셈이 없으면 단일 값 처리
		return evaluateValue(expression, fields, dto, currentRowData, colNameIdx);
	}

	/**
	 * 단일 값 평가 (컬럼명, 숫자, 사업비 데이터)
	 */
	private double evaluateValue(String value, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		value = value.trim();

		// 숫자인 경우 직접 반환
		try {
			return Double.parseDouble(value);
		} catch (NumberFormatException e) {
			// 숫자가 아닌 경우 컬럼값 또는 사업비 데이터 조회
		}

		// 컬럼값 조회 (PV파일 현재 행 또는 DTO 필드)
		String fieldValue = getFieldValue(value, fields, dto, currentRowData, colNameIdx);

		// 사업비 데이터 조회 (bizExp에서 조회)
		if (EnUtility.isBlank(fieldValue)) {
			fieldValue = getBizExpValue(value, currentRowData, colNameIdx);
		}

		// 문자열을 숫자로 변환
		try {
			return Double.parseDouble(fieldValue);
		} catch (NumberFormatException e) {
			throw new IllegalArgumentException("숫자로 변환할 수 없는 값: " + value + " = " + fieldValue);
		}
	}

	/**
	 * 사업비 데이터에서 값 조회
	 */
	private String getBizExpValue(String fieldName, String[] currentRowData, Map<String, Integer> colNameIdx) {
		// 사업비 필드명 목록에서 확인
		List<String> bizExpValueFieldList = Arrays.asList(BizExpenseDto.VALUE_FIELD);
		if (bizExpValueFieldList.contains(fieldName)) {
			Integer colIndex = colNameIdx.get(fieldName);
			if (colIndex != null && colIndex < currentRowData.length) {
				String value = currentRowData[colIndex];
				return value != null ? value.trim() : "0";
			}
		}
		return "0"; // 기본값
	}
	
	/**
	 * 배열 요소값에 사칙연산 적용
	 * 배열 처리 시 각 요소값에 대해 사칙연산을 수행
	 */
	private String processArrayElementValue(String elementValue, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		if (elementValue == null || elementValue.trim().isEmpty()) {
			return elementValue;
		}

		// 사칙연산이 포함된 경우에만 처리
		if (containsArithmeticOperatorsOnly(elementValue)) {
			return processArithmeticExpression(elementValue, fields, dto, currentRowData, colNameIdx);
		}

		// 조건부 변환이 포함된 경우 처리
		if (elementValue.contains("?") && elementValue.contains(":")) {
			return processConditionalValue(elementValue, fields, dto, currentRowData, colNameIdx);
		}

		// 일반 컬럼값 조회
		return getFieldValue(elementValue, fields, dto, currentRowData, colNameIdx);
	}
	
	/**
	 * 논리연산자 포함 여부 체크
	 */
	private boolean containsLogicalOperators(String expression) {
		if (expression == null || expression.trim().isEmpty()) {
			return false;
		}

		// 논리연산자 체크 (&&, ||, !, 괄호, 비교연산자)
		return expression.contains("&&") || expression.contains("||") ||
			   expression.contains("!") || expression.contains("(") || expression.contains(")") ||
			   expression.contains("==") || expression.contains("!=") ||
			   expression.contains(">=") || expression.contains("<=") ||
			   expression.contains(">") || expression.contains("<");
	}

	/**
	 * 복잡한 조건식 평가 (conditionMap용)
	 * 예: (AOD==0&&LRVD==0) || (AOD!=1&&LRVD!=1)
	 */
	private boolean evaluateFilterCondition(String conditionExpression, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		try {
			return evaluateLogicalExpression(conditionExpression, currentRowData, colNameIdx);
		} catch (Exception e) {
			printLog(ILogger.MODE_CHAR_ERROR, "필터 조건 평가 중 오류: " + conditionExpression + ", " + e.getMessage());
			return false; // 오류시 필터링 실패로 처리
		}
	}

	/**
	 * 논리 표현식 평가 (필터링용)
	 */
	private boolean evaluateLogicalExpression(String expression, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		expression = expression.trim();

		// 괄호 처리
		while (expression.contains("(")) {
			int start = expression.lastIndexOf('(');
			int end = expression.indexOf(')', start);
			if (end == -1) {
				throw new IllegalArgumentException("괄호가 올바르게 닫히지 않음: " + expression);
			}

			String subExpression = expression.substring(start + 1, end);
			boolean subResult = evaluateLogicalExpression(subExpression, currentRowData, colNameIdx);
			expression = expression.substring(0, start) + subResult + expression.substring(end + 1);
		}

		// OR 연산자 처리 (||)
		if (expression.contains("||")) {
			String[] orParts = splitByOperator(expression, "||");
			for (String orPart : orParts) {
				if (evaluateLogicalExpression(orPart.trim(), currentRowData, colNameIdx)) {
					return true; // 하나라도 true면 전체 true
				}
			}
			return false;
		}

		// AND 연산자 처리 (&&)
		if (expression.contains("&&")) {
			String[] andParts = splitByOperator(expression, "&&");
			for (String andPart : andParts) {
				if (!evaluateLogicalExpression(andPart.trim(), currentRowData, colNameIdx)) {
					return false; // 하나라도 false면 전체 false
				}
			}
			return true;
		}

		// 단일 조건 처리
		return evaluateFilterSimpleCondition(expression, currentRowData, colNameIdx);
	}

	/**
	 * 단일 필터 조건 평가
	 */
	private boolean evaluateFilterSimpleCondition(String condition, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		condition = condition.trim();

		// NOT 연산자 처리 (!)
		if (condition.startsWith("!")) {
			return !evaluateFilterSimpleCondition(condition.substring(1).trim(), currentRowData, colNameIdx);
		}

		// 비교 연산자 체크 (==, !=, >, <, >=, <=)
		try {
			if (condition.contains("==")) {
				String[] parts = condition.split("==", 2);
				String leftValue = getFilterFieldValue(parts[0].trim(), currentRowData, colNameIdx);
				String rightValue = parts[1].trim();
				return leftValue.equals(rightValue);
			} else if (condition.contains("!=")) {
				String[] parts = condition.split("!=", 2);
				String leftValue = getFilterFieldValue(parts[0].trim(), currentRowData, colNameIdx);
				String rightValue = parts[1].trim();
				return !leftValue.equals(rightValue);
			} else if (condition.contains(">=")) {
				String[] parts = condition.split(">=", 2);
				String leftValue = getFilterFieldValue(parts[0].trim(), currentRowData, colNameIdx);
				String rightValue = parts[1].trim();
				return compareNumeric(leftValue, rightValue) >= 0;
			} else if (condition.contains("<=")) {
				String[] parts = condition.split("<=", 2);
				String leftValue = getFilterFieldValue(parts[0].trim(), currentRowData, colNameIdx);
				String rightValue = parts[1].trim();
				return compareNumeric(leftValue, rightValue) <= 0;
			} else if (condition.contains(">")) {
				String[] parts = condition.split(">", 2);
				String leftValue = getFilterFieldValue(parts[0].trim(), currentRowData, colNameIdx);
				String rightValue = parts[1].trim();
				return compareNumeric(leftValue, rightValue) > 0;
			} else if (condition.contains("<")) {
				String[] parts = condition.split("<", 2);
				String leftValue = getFilterFieldValue(parts[0].trim(), currentRowData, colNameIdx);
				String rightValue = parts[1].trim();
				return compareNumeric(leftValue, rightValue) < 0;
			} else if (condition.contains("=")) {
				String[] parts = condition.split("=", 2);
				String leftValue = getFilterFieldValue(parts[0].trim(), currentRowData, colNameIdx);
				String rightValue = parts[1].trim();
				return leftValue.equals(rightValue);
			} else if ("true".equals(condition)) {
				return true;
			} else if ("false".equals(condition)) {
				return false;
			} else {
				// 단순 값 존재 여부 체크
				String fieldValue = getFilterFieldValue(condition, currentRowData, colNameIdx);
				return EnUtility.isNotBlank(fieldValue);
			}
		} catch (Exception e) {
			// 존재하지 않는 컬럼에 대한 조건은 true로 처리합니다.
			if ("NOT_EXIST_COLUMN".equals(e.getMessage())) {
				return true;
			} else {
				throw e;
			}
		}
	}

	/**
	 * 필터링용 필드값 조회 (간단 버전)
	 */
	private String getFilterFieldValue(String fieldName, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		// 숫자나 문자열 리터럴인 경우 그대로 반환
		if (EnUtility.isNumber(fieldName) || fieldName.startsWith("\"") || fieldName.startsWith("'")) {
			return fieldName.replaceAll("[\"\']", "");
		}

		// 컬럼값 조회
		Integer colIndex = colNameIdx.get(fieldName);

		if (colIndex == null) {
			throw new Exception("NOT_EXIST_COLUMN");
		}

		if (colIndex < currentRowData.length) {
			String colValue = currentRowData[colIndex];
			return colValue != null ? colValue.trim() : "";
		}

		return "";
	}

	/**
	 * 배열 수직 변환 후 t값 기반 삼항연산자 재평가
	 * t 컬럼이 생성된 후 각 행별로 t값을 참조하여 조건부 표현식을 다시 평가
	 */
	private String[] reprocessConditionalExpressionsWithT(String[] rowData, Field[] fields, PvCollatInfoDto dto,
			Map<String, Integer> colNameIdx, List<List<Integer>> convertIdx, List<List<String>> outList) throws Exception {

		// t 컬럼 인덱스 찾기
		Integer tColumnIndex = null;
		for (Map.Entry<String, Integer> entry : colNameIdx.entrySet()) {
			if (entry.getKey().toLowerCase().equals("t")) {
				tColumnIndex = entry.getValue();
				break;
			}
		}

		// t 컬럼이 없으면 원본 반환
		if (tColumnIndex == null) {
			return rowData;
		}

		// 확장된 colNameIdx 생성 (t 컬럼 포함)
		Map<String, Integer> extendedColNameIdx = new HashMap<>(colNameIdx);
		if (tColumnIndex < rowData.length && rowData[tColumnIndex] != null) {
			// t 컬럼이 배열 변환으로 추가된 경우 인덱스 업데이트
			extendedColNameIdx.put("t", tColumnIndex);
		}

		// 각 출력 컬럼에 대해 t값 기반 재평가
		for (int i = 0; i < convertIdx.size() && i < outList.size(); i++) {
			List<String> outExpressions = outList.get(i);
			if (outExpressions == null || outExpressions.isEmpty()) {
				continue;
			}

			for (String expression : outExpressions) {
				if (expression != null && containsConditionalWithT(expression)) {
					try {
						// t값을 참조하는 조건부 표현식 재평가
						String reprocessedValue = processConditionalValue(expression, fields, dto, rowData, extendedColNameIdx);

						// 결과를 해당 컬럼에 적용
						if (i < rowData.length) {
							rowData[i] = reprocessedValue;
						}
					} catch (Exception e) {
						printLog('E', "t값 기반 조건부 표현식 재평가 실패: " + expression + ", " + e.getMessage());
					}
				}
			}
		}

		return rowData;
	}

	/**
	 * 조건부 표현식에 t 컬럼 참조가 포함되어 있는지 확인
	 */
	private boolean containsConditionalWithT(String expression) {
		if (expression == null || !expression.contains("?") || !expression.contains(":")) {
			return false;
		}

		// t 또는 T 컬럼 참조가 포함되어 있는지 확인
		return expression.toLowerCase().contains("t>=") || expression.toLowerCase().contains("t<=") ||
			   expression.toLowerCase().contains("t>") || expression.toLowerCase().contains("t<") ||
			   expression.toLowerCase().contains("t==") || expression.toLowerCase().contains("t!=") ||
			   expression.toLowerCase().contains("t =") || expression.toLowerCase().contains("t=");
	}

	/**
	 * 배열 패턴 포함 여부 확인
	 * 예: [0=값|1=값], t>=1?[0=값|1=값]:[0=값]
	 */
	private boolean containsArrayPattern(String expression) {
		if (expression == null) return false;

		// [숫자=값|숫자=값] 패턴 감지
		return expression.matches(".*\\[\\d+=.*\\|.*\\d+=.*\\].*") ||
			   expression.matches(".*\\[\\d+=.*\\].*");
	}

	/**
	 * 배열 표현식 처리 (getConvValue에서 직접 처리)
	 * 예: "t>=1 ? [0=기준가입금액*1.2|1=기준가입금액*0.8] : [0=기준가입금액*0.5]"
	 */
	private String processArrayExpression(String expression, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		try {
			// t 조건이 포함된 경우 가상의 t값으로 평가
			if (containsTIndexCondition(expression)) {
				return processArrayWithTCondition(expression, fields, dto, currentRowData, colNameIdx);
			}

			// 단순 배열인 경우 첫 번째 요소 반환 (기본 동작)
			if (expression.startsWith("[") && expression.endsWith("]")) {
				return processSimpleArray(expression, fields, dto, currentRowData, colNameIdx);
			}

			// 조건부 배열인 경우 조건 평가 후 배열 처리
			if (expression.contains("?") && expression.contains(":")) {
				return processConditionalArray(expression, fields, dto, currentRowData, colNameIdx);
			}

			return expression; // 처리할 수 없는 경우 원본 반환
		} catch (Exception e) {
			printLog('E', "배열 표현식 처리 중 오류: " + expression + ", " + e.getMessage());
			return expression; // 오류시 원본 반환
		}
	}

	/**
	 * t 인덱스를 참조하는 조건인지 확인
	 */
	private boolean containsTIndexCondition(String expression) {
		if (expression == null) return false;

		// t>=, t<=, t==, t!= 등의 패턴
		return expression.matches(".*\\bt\\s*[><=!]+\\s*\\d+.*");
	}

	/**
	 * t 조건이 포함된 배열 처리
	 * 배열의 모든 인덱스에 대해 조건을 평가하여 결과 배열 생성
	 */
	private String processArrayWithTCondition(String expression, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		// 배열에서 가능한 t값들 추출
		Set<Integer> tValues = extractArrayIndices(expression);

		StringBuilder resultArray = new StringBuilder("[");
		boolean first = true;

		// 각 t값에 대해 조건 평가
		for (Integer t : tValues) {
			// 임시로 t값을 설정하여 조건 평가
			Map<String, Integer> tempColNameIdx = new HashMap<>(colNameIdx);
			String[] tempRowData = Arrays.copyOf(currentRowData, currentRowData.length + 1);

			// t 컬럼 추가
			tempColNameIdx.put("t", tempRowData.length - 1);
			tempRowData[tempRowData.length - 1] = String.valueOf(t);

			// 조건 평가
			String result = processConditionalValue(expression, fields, dto, tempRowData, tempColNameIdx);

			// 결과가 배열인 경우 해당 t값의 요소 추출
			if (result.startsWith("[") && result.endsWith("]")) {
				String elementValue = extractArrayElement(result, t);
				if (elementValue != null) {
					if (!first) resultArray.append("|");
					resultArray.append(t).append("=").append(elementValue);
					first = false;
				}
			} else {
				// 단순 값인 경우
				if (!first) resultArray.append("|");
				resultArray.append(t).append("=").append(result);
				first = false;
			}
		}

		resultArray.append("]");
		return resultArray.toString();
	}

	/**
	 * 표현식에서 배열 인덱스들 추출
	 */
	private Set<Integer> extractArrayIndices(String expression) {
		Set<Integer> indices = new HashSet<>();

		// [숫자=값|숫자=값] 패턴에서 숫자들 추출
		Pattern pattern = Pattern.compile("\\[(\\d+=.*?)\\]");
		Matcher matcher = pattern.matcher(expression);

		while (matcher.find()) {
			String arrayContent = matcher.group(1);
			String[] elements = arrayContent.split("\\|");

			for (String element : elements) {
				String[] keyValue = element.split("=", 2);
				if (keyValue.length == 2) {
					try {
						indices.add(Integer.parseInt(keyValue[0].trim()));
					} catch (NumberFormatException e) {
						// 숫자가 아닌 경우 무시
					}
				}
			}
		}

		return indices;
	}

	/**
	 * 배열에서 특정 인덱스의 값 추출
	 */
	private String extractArrayElement(String arrayStr, int index) {
		if (!arrayStr.startsWith("[") || !arrayStr.endsWith("]")) {
			return null;
		}

		String content = arrayStr.substring(1, arrayStr.length() - 1);
		String[] elements = content.split("\\|");

		for (String element : elements) {
			String[] keyValue = element.split("=", 2);
			if (keyValue.length == 2) {
				try {
					int key = Integer.parseInt(keyValue[0].trim());
					if (key == index) {
						return keyValue[1].trim();
					}
				} catch (NumberFormatException e) {
					// 숫자가 아닌 경우 무시
				}
			}
		}

		return null;
	}

	/**
	 * 단순 배열 처리 (조건 없음)
	 */
	private String processSimpleArray(String arrayStr, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		// 배열의 각 요소에 사칙연산 적용
		if (!arrayStr.startsWith("[") || !arrayStr.endsWith("]")) {
			return arrayStr;
		}

		String content = arrayStr.substring(1, arrayStr.length() - 1);
		String[] elements = content.split("\\|");
		StringBuilder result = new StringBuilder("[");

		boolean first = true;
		for (String element : elements) {
			String[] keyValue = element.split("=", 2);
			if (keyValue.length == 2) {
				String key = keyValue[0].trim();
				String value = keyValue[1].trim();

				// 값에 사칙연산 적용
				String processedValue = processArrayElementValue(value, fields, dto, currentRowData, colNameIdx);

				if (!first) result.append("|");
				result.append(key).append("=").append(processedValue);
				first = false;
			}
		}

		result.append("]");
		return result.toString();
	}

	/**
	 * 조건부 배열 처리 (삼항연산자 포함)
	 */
	private String processConditionalArray(String expression, Field[] fields, PvCollatInfoDto dto, String[] currentRowData, Map<String, Integer> colNameIdx) throws Exception {
		// 조건부 변환 처리 후 결과가 배열이면 배열 처리
		String result = processConditionalValue(expression, fields, dto, currentRowData, colNameIdx);

		if (result.startsWith("[") && result.endsWith("]")) {
			return processSimpleArray(result, fields, dto, currentRowData, colNameIdx);
		}

		return result;
	}
}
